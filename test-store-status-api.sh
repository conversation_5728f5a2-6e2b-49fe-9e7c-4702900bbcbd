#!/bin/bash

# POS门店状态API测试脚本
# 测试获取门店下的在线、离线、上下机信息接口

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BASE_URL="http://localhost:8081/api/v1"
STORE_NO=1001

# 显示帮助信息
show_help() {
    echo "POS门店状态API测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -u, --url URL       设置API基础URL (默认: $BASE_URL)"
    echo "  -s, --store STORE   设置门店编号 (默认: $STORE_NO)"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认配置测试"
    echo "  $0 -s 1002                           # 测试门店1002"
    echo "  $0 -u http://localhost:8080/api/v1   # 使用不同的URL"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        -s|--store)
            STORE_NO="$2"
            shift 2
            ;;
        *)
            echo "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

echo -e "${BLUE}=== POS门店状态API测试 ===${NC}"
echo -e "${YELLOW}API基础URL: $BASE_URL${NC}"
echo -e "${YELLOW}测试门店编号: $STORE_NO${NC}"
echo ""

# 测试1: POST方式查询门店状态
echo -e "${BLUE}测试1: POST方式查询门店状态${NC}"
echo "请求URL: $BASE_URL/api/pos/store-status/query"
echo "请求方法: POST"
echo "请求体: {\"storeNo\": $STORE_NO}"
echo ""

response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" \
    -d "{\"storeNo\": $STORE_NO}" \
    "$BASE_URL/api/pos/store-status/query")

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ POST方式测试成功${NC}"
else
    echo -e "${RED}✗ POST方式测试失败${NC}"
fi
echo ""

# 测试2: GET方式查询门店状态
echo -e "${BLUE}测试2: GET方式查询门店状态${NC}"
echo "请求URL: $BASE_URL/api/pos/store-status/query/$STORE_NO"
echo "请求方法: GET"
echo ""

response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X GET \
    -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" \
    "$BASE_URL/api/pos/store-status/query/$STORE_NO")

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

if [ "$http_code" = "200" ]; then
    echo -e "${GREEN}✓ GET方式测试成功${NC}"
else
    echo -e "${RED}✗ GET方式测试失败${NC}"
fi
echo ""

# 测试3: 参数验证测试（空门店编号）
echo -e "${BLUE}测试3: 参数验证测试（空门店编号）${NC}"
echo "请求URL: $BASE_URL/api/pos/store-status/query"
echo "请求方法: POST"
echo "请求体: {}"
echo ""

response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
    -X POST \
    -H "Content-Type: application/json" \
    -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" \
    -d "{}" \
    "$BASE_URL/api/pos/store-status/query")

http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_CODE:/d')

echo "HTTP状态码: $http_code"
echo "响应内容:"
echo "$response_body" | jq . 2>/dev/null || echo "$response_body"
echo ""

if [ "$http_code" = "400" ]; then
    echo -e "${GREEN}✓ 参数验证测试成功（正确返回400错误）${NC}"
else
    echo -e "${YELLOW}⚠ 参数验证测试结果异常（期望400，实际$http_code）${NC}"
fi
echo ""

echo -e "${BLUE}=== 测试完成 ===${NC}"
echo ""
echo "注意事项："
echo "1. 确保应用已启动并运行在指定端口"
echo "2. 确保外部API服务可访问"
echo "3. 如果测试失败，请检查应用日志"
echo "4. 开发环境API地址: http://10.211.241.215:8080/api/pos/getPosByStore"
echo "5. 生产环境API地址: http://rt-pos-api.idc1.fn/api/pos/getPosByStore"
