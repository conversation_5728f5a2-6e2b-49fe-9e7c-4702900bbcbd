package com.rtpos.server.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rtpos.server.dto.auth.*;
import com.rtpos.server.service.UserAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 用户认证服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAuthServiceImpl implements UserAuthService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${auth.api.send-code-url}")
    private String sendCodeUrl;

    @Value("${auth.api.login-url}")
    private String loginUrl;

    @Value("${auth.api.query-emp-url}")
    private String queryEmpUrl;

    @Value("${auth.api.logout-url}")
    private String logoutUrl;

    @Value("${auth.api.validate-token-url}")
    private String validateTokenUrl;

    @Override
    public AuthResponse<Object> sendCode(SendCodeRequest request) {
        log.info("Sending verification code to mobile: {}", request.getMobile());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"mobile\":\"%s\",\"app\":\"%s\"}",
                    request.getMobile(), request.getApp());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse<Object>> response = restTemplate.exchange(
                    sendCodeUrl, HttpMethod.POST, entity,
                    new ParameterizedTypeReference<AuthResponse<Object>>() {}
            );

            log.info("Send code response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to send verification code", e);
            AuthResponse<Object> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("发送验证码失败: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public AuthResponse<LoginData> login(LoginRequest request) {
        log.info("User login with mobile: {}", request.getMobile());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"mobile\":\"%s\",\"app\":\"%s\",\"code\":\"%s\",\"type\":%d}",
                    request.getMobile(), request.getApp(), request.getCode(), request.getType());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse<LoginData>> response = restTemplate.exchange(
                    loginUrl, HttpMethod.POST, entity,
                    new ParameterizedTypeReference<AuthResponse<LoginData>>() {}
            );

            log.info("Login response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to login", e);
            AuthResponse<LoginData> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("登录失败: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public AuthResponse<List<EmpDeptRole>> queryEmpDeptRole(QueryEmpRequest request) {
        log.info("Querying employee dept role for empId: {}", request.getEmpId());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"emp_id\":\"%s\"}", request.getEmpId());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            ResponseEntity<AuthResponse<List<EmpDeptRole>>> response = restTemplate.exchange(
                    queryEmpUrl, HttpMethod.POST, entity,
                    new ParameterizedTypeReference<AuthResponse<List<EmpDeptRole>>>() {}
            );

            log.info("Query emp dept role response: {}", response.getBody());
            return response.getBody();

        } catch (Exception e) {
            log.error("Failed to query employee dept role", e);
            AuthResponse<List<EmpDeptRole>> errorResponse = new AuthResponse<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("查询员工信息失败: " + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public POSAuthResponse<Object> logout(LogoutRequest request) {
        log.info("User logout with token: {}", request.getToken());

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_FORM_URLENCODED_VALUE));

            // 构建JSON数据
            String jsonData = String.format("{\"token\":\"%s\"}", request.getToken());

            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("data", jsonData);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(formData, headers);

            // 调用外部登出API，返回格式: {"code":200,"msg":"退出成功！"}
            ResponseEntity<AuthResponse<Object>> response = restTemplate.exchange(
                    logoutUrl, HttpMethod.POST, entity,
                    new ParameterizedTypeReference<AuthResponse<Object>>() {}
            );

            log.info("Logout response: {}", response.getBody());

            // 将外部API响应转换为POSAuthResponse格式
            POSAuthResponse<Object> posResult = new POSAuthResponse<>();
            AuthResponse<Object> authResponse = response.getBody();

            if (authResponse != null && authResponse.getCode() == 200) {
                posResult.setRsCode("00000000");
                posResult.setMsg(authResponse.getMsg());
                posResult.setBody(authResponse.getData());
            } else {
                posResult.setRsCode("9999-" + (authResponse != null ? authResponse.getCode() : 500));
                posResult.setMsg(authResponse != null ? authResponse.getMsg() : "登出失败");
                posResult.setBody(null);
            }

            return posResult;

        } catch (Exception e) {
            log.error("Failed to logout", e);
            POSAuthResponse<Object> errorResponse = new POSAuthResponse<>();
            errorResponse.setRsCode("99999999");
            errorResponse.setMsg("登出失败: " + e.getMessage());
            errorResponse.setBody(null);
            return errorResponse;
        }
    }

    @Override
    public POSAuthResponse<String> validateToken(String token) {
        log.info("Validating token: {}", token);

        try {
            String url = validateTokenUrl + "?token=" + token;

            ResponseEntity<TokenValidationResponse> response = restTemplate.exchange(
                    url, HttpMethod.GET, null,
                    new ParameterizedTypeReference<TokenValidationResponse>() {}
            );

            log.info("Token validation response: {}", response.getBody());

            // 将外部API响应转换为POSAuthResponse格式
            POSAuthResponse<String> posResult = new POSAuthResponse<>();
            TokenValidationResponse validationResponse = response.getBody();

            if (validationResponse != null) {
                posResult.setRsCode(validationResponse.getRsCode());
                posResult.setMsg(validationResponse.getMsg());
                posResult.setBody(validationResponse.getBody());
            } else {
                posResult.setRsCode("99999999");
                posResult.setMsg("Token验证失败");
                posResult.setBody(null);
            }

            return posResult;

        } catch (Exception e) {
            log.error("Failed to validate token", e);
            POSAuthResponse<String> errorResponse = new POSAuthResponse<>();
            errorResponse.setRsCode("99999999");
            errorResponse.setMsg("Token验证失败: " + e.getMessage());
            errorResponse.setBody(null);
            return errorResponse;
        }
    }
}
