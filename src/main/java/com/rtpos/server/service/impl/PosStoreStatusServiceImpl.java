package com.rtpos.server.service.impl;

import com.rtpos.server.dto.PosStoreStatusRequest;
import com.rtpos.server.dto.PosStoreStatusResponse;
import com.rtpos.server.service.PosStoreStatusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * POS门店状态服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PosStoreStatusServiceImpl implements PosStoreStatusService {

    private final RestTemplate restTemplate;

    @Value("${pos.api.store-status-base-url}")
    private String apiBaseUrl;

    @Value("${pos.api.store-status-path}")
    private String storeStatusPath;

    @Override
    public PosStoreStatusResponse getStoreStatus(PosStoreStatusRequest request) {
        log.info("Getting store status from API for store: {}", request.getStoreNo());

        try {
            String url = apiBaseUrl + storeStatusPath;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<PosStoreStatusRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<PosStoreStatusResponse> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, PosStoreStatusResponse.class);
            
            PosStoreStatusResponse result = response.getBody();
            
            if (result != null && result.isSuccess()) {
                log.info("Successfully fetched store status from API for store: {}", request.getStoreNo());
                if (result.getBody() != null) {
                    log.debug("Store status details - Total POS: {}, Online: {}, Offline: {}, Login: {}, Logout: {}", 
                            result.getBody().getAllPos(),
                            result.getBody().getOnline() != null ? result.getBody().getOnline().size() : 0,
                            result.getBody().getOffline() != null ? result.getBody().getOffline().size() : 0,
                            result.getBody().getLogin() != null ? result.getBody().getLogin().size() : 0,
                            result.getBody().getLogout() != null ? result.getBody().getLogout().size() : 0);
                }
            } else {
                log.warn("API returned error response for store {}: {}", 
                        request.getStoreNo(), result != null ? result.getMsg() : "null response");
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("Failed to get store status from API for store: {}", request.getStoreNo(), e);
            throw new RuntimeException("获取门店状态信息失败: " + e.getMessage(), e);
        }
    }
}
