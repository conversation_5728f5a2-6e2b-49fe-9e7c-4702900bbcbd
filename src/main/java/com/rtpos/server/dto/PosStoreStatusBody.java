package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * POS门店状态响应数据体DTO
 * 对应外部接口 getPosByStore 返回的数据结构
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS门店状态响应数据体")
public class PosStoreStatusBody {

    @Schema(description = "总设备数量", example = "105")
    @JsonProperty("allPos")
    private Integer allPos;

    @Schema(description = "所有门店在线机台数", example = "12000")
    @JsonProperty("allOnlPosCount")
    private Integer allOnlPosCount;

    @Schema(description = "所有门店离线机台数", example = "8999")
    @JsonProperty("alloffPosCount")
    private Integer alloffPosCount;

    @Schema(description = "在线机台集合，如果没有返回空集合")
    @JsonProperty("online")
    private List<Integer> online;

    @Schema(description = "离线机台集合，如果没有返回空集合")
    @JsonProperty("offline")
    private List<Integer> offline;

    @Schema(description = "上机状态机台集合，如果没有返回空集合")
    @JsonProperty("login")
    private List<Integer> login;

    @Schema(description = "下机状态机台集合，如果没有返回空集合")
    @JsonProperty("logout")
    private List<Integer> logout;
}
